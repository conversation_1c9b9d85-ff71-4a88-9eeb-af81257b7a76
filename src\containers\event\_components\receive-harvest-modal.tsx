/* eslint-disable @typescript-eslint/no-explicit-any */
'use client';

import { zodResolver } from '@hookform/resolvers/zod';
import CheckIcon from '@mui/icons-material/Check';
import CloseIcon from '@mui/icons-material/Close';
import InfoOutlinedIcon from '@mui/icons-material/InfoOutlined';
import {
  Alert,
  Box,
  Button,
  Checkbox,
  CircularProgress,
  Dialog,
  DialogActions,
  DialogContent,
  FormControlLabel,
  IconButton,
  List,
  ListItem,
  ListItemText,
  TextField,
  Typography,
} from '@mui/material';
import { useQuery } from '@tanstack/react-query';
import { CustomDatePicker } from 'components/date-picker/custom-date-picker';
import dayjs from 'dayjs';
import { useTranslations } from 'next-intl';
import { ChangeEvent, FC, useCallback, useEffect, useMemo, useState } from 'react';
import { Controller, useForm, useWatch } from 'react-hook-form';
import { fetchVarietiesService } from 'services/resource.service';
import { useMasterDataStore } from 'store/useMaterDataStore';
import { theme } from 'styles/theme';
import { DurianGrade, DurianVariety, ReceiveUpdatePayload } from 'types';
import { formatNumberWithCommas, isValidNumberInput } from 'utils';
import {
  cleanDecimalInput,
  ensureVarietyInMap,
  findVarietyAndGrade,
  hasLeadingZero,
  isValidValue,
  normalizeValue,
  processDecimalValue,
  processIntegerValue,
} from 'utils/input-grade';
import * as z from 'zod';

interface VarietiesModalProps {
  open: boolean;
  onClose?: () => void;
  onSave?: (data: ReceiveUpdatePayload) => Promise<void>;
  varieties?: DurianVariety[];
  initialWeights?: Record<string, string>;
  initialCustomName?: string;
  useProvidedVarieties?: boolean;
  editableVarietyIds?: Set<string>;
  varietyBloomDays?: Record<string, number>;
  isEditReceiving: boolean; // NEW: determines component behavior
}

// Conditional FormData types based on mode
type EditModeFormData = {
  weights: Record<string, string | undefined>;
  customVarietyName?: string;
};

type ConfirmModeFormData = EditModeFormData & {
  confirmReceiveHarvest: boolean;
  awareUndoAction: boolean;
};

type FormData = EditModeFormData | ConfirmModeFormData;

export const ReceiveHarvestModal: FC<VarietiesModalProps> = ({
  open,
  onClose,
  onSave,
  varieties: providedVarieties = [],
  initialWeights = {},
  initialCustomName = '',
  useProvidedVarieties = false,
  editableVarietyIds = new Set(),
  varietyBloomDays = {},
  isEditReceiving,
}) => {
  const { getVarietyLabel, getGradeLabel } = useMasterDataStore();
  const receiveTranslation = useTranslations('receive');
  const commonT = useTranslations('common');
  const [selectedVariety, setSelectedVariety] = useState<string | null>(null);
  const [varietiesWithData, setVarietiesWithData] = useState<string[]>([]);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitError, setSubmitError] = useState<string | null>(null);
  const [allZeroOrNegativeWarning, setAllZeroOrNegativeWarning] = useState<boolean>(false);
  const [internalCustomName, setInternalCustomName] = useState(initialCustomName);

  const allZeroOrNegativeWarningMessage = receiveTranslation('receive-all-zero-or-negative-warning-message');

  const {
    data: varietiesResponse,
    isLoading: isLoadingVarieties,
    error: varietiesError,
  } = useQuery({
    queryKey: ['varieties'],
    queryFn: fetchVarietiesService,
    enabled: open && !useProvidedVarieties,
  });

  const varieties = useMemo(() => {
    if (useProvidedVarieties) {
      return providedVarieties;
    }
    return varietiesResponse?.data || [];
  }, [useProvidedVarieties, providedVarieties, varietiesResponse]);

  const createWeightsSchema = () => {
    const weightsShape: Record<string, z.ZodType<any>> = {};

    varieties.forEach((variety) => {
      variety.grades.forEach((grade) => {
        const key = `${variety.id}-${grade.id}`;
        weightsShape[key] = z.string().optional();
      });
    });

    return z.record(z.string(), z.string().optional());
  };

  // Conditional form schema based on mode
  const formSchema = useMemo(() => {
    const baseSchema = {
      weights: createWeightsSchema(),
      customVarietyName: z.string().optional(),
    };

    if (isEditReceiving) {
      // Edit mode: only base fields
      return z.object(baseSchema);
    } else {
      // Confirm mode: add checkbox fields
      return z.object({
        ...baseSchema,
        confirmReceiveHarvest: z.boolean(),
        awareUndoAction: z.boolean(),
      });
    }
    // todo: need to fix
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [varieties, isEditReceiving]);

  // Conditional default values based on mode
  const getDefaultValues = useMemo(() => {
    const baseDefaults = {
      weights: initialWeights as Record<string, string | undefined>,
      customVarietyName: initialCustomName,
    };

    if (isEditReceiving) {
      return baseDefaults;
    } else {
      return {
        ...baseDefaults,
        confirmReceiveHarvest: false,
        awareUndoAction: false,
      };
    }
  }, [initialWeights, initialCustomName, isEditReceiving]);

  const {
    control,
    handleSubmit,
    reset,
    setValue,
    formState: { errors },
  } = useForm<FormData>({
    resolver: zodResolver(formSchema),
    defaultValues: getDefaultValues,
    mode: 'onChange',
  });

  const weights = useWatch({
    control,
    name: 'weights',
    defaultValue: initialWeights as Record<string, string | undefined>,
  });

  const customVarietyName = useWatch({
    control,
    name: 'customVarietyName',
    defaultValue: initialCustomName,
  });

  // Conditional useWatch for checkboxes only in confirm mode
  const confirmReceiveHarvest = useWatch({
    control,
    name: 'confirmReceiveHarvest' as any,
    defaultValue: false,
  });

  const awareUndoAction = useWatch({
    control,
    name: 'awareUndoAction' as any,
    defaultValue: false,
  });

  // Calculate loading state
  const isLoading = (!useProvidedVarieties && isLoadingVarieties) || isSubmitting;

  // Button enabled logic based on mode
  const isButtonEnabled = useMemo(() => {
    const baseCondition =
      !isLoading && (!useProvidedVarieties ? !!varietiesResponse : true) && !allZeroOrNegativeWarning;

    if (isEditReceiving) {
      // Edit mode: only base conditions
      return baseCondition;
    } else {
      // Confirm mode: add checkbox conditions
      return baseCondition && confirmReceiveHarvest && awareUndoAction;
    }
  }, [
    isLoading,
    useProvidedVarieties,
    varietiesResponse,
    allZeroOrNegativeWarning,
    isEditReceiving,
    confirmReceiveHarvest,
    awareUndoAction,
  ]);

  // Button configuration based on mode
  const buttonConfig = useMemo(() => {
    if (isEditReceiving) {
      return {
        text: commonT('save-modal-btn'),
        id: 'save-modal-btn',
        className: 'save-modal-btn',
      };
    } else {
      return {
        text: commonT('confirm-modal-btn'),
        id: 'confirm-modal-btn',
        className: 'confirm-modal-btn',
      };
    }
  }, [isEditReceiving, commonT]);

  useEffect(() => {
    if (open) {
      setInternalCustomName(initialCustomName);

      // Conditional reset based on mode
      if (isEditReceiving) {
        reset({
          weights: initialWeights as Record<string, string | undefined>,
          customVarietyName: initialCustomName,
        });
      } else {
        reset({
          weights: initialWeights as Record<string, string | undefined>,
          customVarietyName: initialCustomName,
          confirmReceiveHarvest: false,
          awareUndoAction: false,
        } as any);
      }

      setSubmitError(null);
    }
  }, [open, initialWeights, reset, initialCustomName, isEditReceiving]);

  useEffect(() => {
    const varietiesWithValues = new Set<string>();
    Object.entries(weights).forEach(([key, value]) => {
      if (value && value.trim() !== '') {
        const varietyId = key.split('-')[0];
        if (varietyId) {
          varietiesWithValues.add(varietyId);
        }
      }
    });

    if (customVarietyName && customVarietyName.trim() !== '') {
      const otherVariety = varieties.find((v) => v.value === 'other');
      if (otherVariety) {
        varietiesWithValues.add(otherVariety.id);
      }
    }

    const newVarietiesWithData = Array.from(varietiesWithValues);
    if (
      JSON.stringify(newVarietiesWithData.toSorted((a, b) => a.localeCompare(b))) !==
      JSON.stringify([...varietiesWithData].toSorted((a, b) => a.localeCompare(b)))
    ) {
      setVarietiesWithData(newVarietiesWithData);
    }
  }, [weights, customVarietyName, varieties, varietiesWithData]);

  useEffect(() => {
    if (open && varieties.length > 0 && !selectedVariety) {
      setSelectedVariety(varieties[0].id);
    }
  }, [open, varieties, selectedVariety]);

  const prepareApiPayload = (
    formData: FormData
  ): {
    update: {
      varieties: Array<{
        id: string;
        grades: Array<{ id: string; weight: number; name?: string }>;
        flowerBloomingDay?: number;
      }>;
      clientName?: string;
    };
  } => {
    const varietiesMap: Record<
      string,
      {
        id: string;
        grades: Array<{ id: string; weight: number; name?: string }>;
        flowerBloomingDay: number;
      }
    > = {};

    Object.entries(formData.weights || {}).forEach(([key, value]) => {
      if (!isValidValue(value)) return;

      const { varietyId, grade } = findVarietyAndGrade(key, varieties);

      if (!varietyId || !grade) return;

      ensureVarietyInMap(varietyId, varietiesMap, varietyBloomDays);

      varietiesMap[varietyId].grades.push({
        id: grade.id,
        weight: Number((value ?? '').trim()),
      });
    });

    const otherVariety = varieties.find((v) => v.value === 'other');

    if (otherVariety) {
      let otherGrade = otherVariety.grades.find((g) => g.value === 'D');

      if (!otherGrade && otherVariety.grades.length > 0) {
        otherGrade = otherVariety.grades[0];
      }

      if (otherGrade) {
        const currentName = internalCustomName || '';
        if (currentName.trim() !== '' && varietiesMap[otherVariety.id]) {
          const _otherGrade = varietiesMap[otherVariety.id].grades.map((grade) => ({ ...grade, name: currentName }));
          varietiesMap[otherVariety.id] = {
            id: otherVariety.id,
            grades: _otherGrade,
            flowerBloomingDay: varietyBloomDays[otherVariety.id],
          };
        }
      }
    }
    const varietiesArray = Object.values(varietiesMap);

    return {
      update: {
        varieties: varietiesArray,
        clientName: internalCustomName,
      },
    };
  };

  useEffect(() => {
    if (selectedVariety && weights) {
      const selectedVarietyData = varieties.find((v) => v.id === selectedVariety);
      if (selectedVarietyData) {
        const gradeKeys = selectedVarietyData.grades.map((grade) => `${selectedVariety}-${grade.id}`);
        const enteredValues = gradeKeys.map((key) => weights[key]);
        const allValuesLessThanOrEqualToZero =
          enteredValues.length > 0 &&
          enteredValues.every((value: any) => {
            if (!value) return true;
            const numValue = parseFloat(value);
            return numValue <= 0;
          });

        setAllZeroOrNegativeWarning(allValuesLessThanOrEqualToZero);
      }
    } else {
      setAllZeroOrNegativeWarning(false);
    }
  }, [weights, selectedVariety, varieties]);

  const onSubmit = async (data: FormData) => {
    try {
      setIsSubmitting(true);
      setSubmitError(null);
      const payload = prepareApiPayload({
        ...data,
        customVarietyName: internalCustomName,
      });
      if (payload.update.varieties.length === 0) {
        return;
      }
      if (onSave) {
        await onSave(payload);
      }
    } catch (error) {
      setSubmitError(error instanceof Error ? error.message : commonT('unknown-error'));
    } finally {
      setIsSubmitting(false);
      handleClose();
    }
  };

  const handleClose = () => {
    onClose?.();
  };

  const isVarietyEditable = useCallback(
    (varietyId: string) => {
      return editableVarietyIds.has(varietyId);
    },
    [editableVarietyIds]
  );

  const handleVarietySelect = useCallback(
    (varietyId: string) => {
      if (isVarietyEditable(varietyId)) {
        setSelectedVariety(varietyId);
      }
    },
    [isVarietyEditable]
  );

  const selectedVarietyData = varieties.find((variety) => variety.id === selectedVariety);

  const checkVarietyHasData = (varietyId: string) => {
    const otherVariety = varieties.find((v) => v.id === varietyId && v.value === 'other');
    if (otherVariety && customVarietyName && customVarietyName.trim() !== '') {
      return true;
    }
    for (const [key, value] of Object.entries(weights)) {
      if (key.startsWith(varietyId) && value && value.trim() !== '') {
        return true;
      }
    }
    return false;
  };

  const isOtherVariety = (varietyId: string) => {
    const variety = varieties.find((v) => v.id === varietyId);
    return variety?.value === 'other';
  };

  const firstEditableVariety = useMemo(() => {
    return varieties.find((variety) => isVarietyEditable(variety.id))?.id ?? null;
  }, [varieties, isVarietyEditable]);

  useEffect(() => {
    if (open) {
      if (isEditReceiving) {
        reset({ weights: initialWeights });
      } else {
        reset({
          weights: initialWeights,
          confirmReceiveHarvest: false,
          awareUndoAction: false,
        } as any);
      }
      setSelectedVariety(firstEditableVariety);
      setAllZeroOrNegativeWarning(false);
    }
  }, [open, initialWeights, varieties, reset, firstEditableVariety, isEditReceiving]);

  const selectedVarietyBloomDay = useMemo(() => {
    if (selectedVariety && varietyBloomDays[selectedVariety]) {
      return varietyBloomDays[selectedVariety];
    }
    return null;
  }, [selectedVariety, varietyBloomDays]);

  const formattedBloomDay = useMemo(() => {
    if (selectedVarietyBloomDay) {
      const timestamp =
        typeof selectedVarietyBloomDay === 'string'
          ? Number.parseInt(selectedVarietyBloomDay, 10)
          : selectedVarietyBloomDay;

      if (!isNaN(timestamp)) {
        return dayjs(timestamp * 1000);
      }
    }
    return null;
  }, [selectedVarietyBloomDay]);

  const handleCustomVarietyNameOnBlur = () => {
    setValue('customVarietyName', internalCustomName);
  };

  const handleCustomVarietyNameOnChange = (e: ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const newValue = e.target.value;
    setInternalCustomName(newValue);
    setValue('customVarietyName', newValue);
  };

  const renderGradeInputsSide = () => {
    if (!selectedVariety) return;
    return (() => {
      const variety = varieties.find((v) => v.id === selectedVariety);
      if (!variety) return null;

      return (
        <>
          {isOtherVariety(variety.id) && (
            <Controller
              name="customVarietyName"
              control={control}
              render={({ field }) => (
                <>
                  <Typography sx={{ fontSize: '18px' }} variant="caption">
                    {commonT('variety-name')}{' '}
                    <Typography variant="caption" color="error">
                      *
                    </Typography>
                  </Typography>
                  <TextField
                    {...field}
                    fullWidth
                    variant="outlined"
                    margin="normal"
                    value={internalCustomName || ''}
                    onChange={handleCustomVarietyNameOnChange}
                    onBlur={handleCustomVarietyNameOnBlur}
                    error={!!errors.customVarietyName}
                    helperText={errors.customVarietyName?.message}
                    disabled
                    placeholder="Enter variety name (optional)"
                    sx={{
                      mb: 2,
                      '& .MuiOutlinedInput-root': {
                        bgcolor: 'white',
                      },
                    }}
                  />
                </>
              )}
            />
          )}
          {isVarietyEditable(selectedVariety) && (
            <>
              <Typography sx={{ fontSize: '18px' }} variant="caption">
                {receiveTranslation('flower-blooming-day')}{' '}
                <Typography variant="caption" color="error">
                  *
                </Typography>
              </Typography>
              <CustomDatePicker
                value={formattedBloomDay}
                disabled={true}
                slotProps={{
                  textField: {
                    variant: 'outlined',
                    fullWidth: true,
                    margin: 'normal',
                  },
                }}
                onChange={() => {}}
                sx={{
                  mb: 2,
                  '& .MuiOutlinedInput-root': {
                    bgcolor: 'white',
                  },
                }}
              />
            </>
          )}
          {variety.grades.map((grade) => renderGradeInput(variety, grade))}
        </>
      );
    })();
  };

  const renderGradeInput = (variety: DurianVariety, grade: DurianGrade) => {
    return (
      <Controller
        key={`${variety.id}-${grade.id}`}
        name={`weights.${variety.id}-${grade.id}`}
        control={control}
        render={({ field }) => (
          <>
            <Typography sx={{ fontSize: '18px', mb: 1 }} variant="caption">
              {getGradeLabel(grade.id)}{' '}
            </Typography>
            <TextField
              {...field}
              fullWidth
              value={formatNumberWithCommas(field.value)}
              variant="outlined"
              margin="normal"
              error={!!errors.weights?.[`${variety.id}-${grade.id}`]}
              helperText={errors.weights?.[`${variety.id}-${grade.id}`]?.message}
              onChange={(e: React.ChangeEvent<HTMLInputElement>) => {
                const rawValue = e.target.value.replace(/,/g, '');

                // Early return for empty value
                if (!rawValue) {
                  field.onChange('');
                  return;
                }

                // Early return for invalid input or leading zeros
                if (!isValidNumberInput(rawValue) || hasLeadingZero(rawValue)) {
                  return;
                }

                // Clean and normalize the value
                const cleanValue = cleanDecimalInput(rawValue);
                const processedValue = normalizeValue(cleanValue);

                // Process based on whether it has a decimal point
                const hasDot = processedValue.includes('.');
                const finalValue = hasDot ? processDecimalValue(processedValue) : processIntegerValue(processedValue);

                // Early return if validation failed
                if (finalValue === null) {
                  return;
                }

                // Update field value
                field.onChange(finalValue);
              }}
              onBlur={field.onBlur}
              disabled={isSubmitting || !isVarietyEditable(selectedVarietyData?.id as string)}
              sx={{
                mb: 2,
                '& .MuiOutlinedInput-root': {
                  bgcolor: 'white',
                },
              }}
            />
          </>
        )}
      />
    );
  };

  const renderVarietyColor = (id: string) => {
    if (selectedVariety === id) {
      return theme.palette.customColors.blueHighlight;
    }
    if (isVarietyEditable(id)) {
      return 'text.primary';
    }
    return 'text.disabled';
  };

  const renderVarietyBgColor = (id: string) => {
    if (isVarietyEditable(id)) {
      if (selectedVariety === id) {
        return theme.palette.customColors.lightGray;
      }
      return 'rgba(0, 0, 0, 0.04)';
    }
    return 'transparent';
  };

  return (
    <Dialog
      open={open}
      onClose={handleClose}
      maxWidth="md"
      fullWidth
      slotProps={{
        paper: {
          sx: { borderRadius: 1 },
        },
      }}
    >
      <Box
        sx={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          p: 2,
          pb: 1,
        }}
      >
        <Typography component="div" variant="h6">
          {isEditReceiving ? receiveTranslation('varieties-update') : receiveTranslation('confirm-harvest-data')}
        </Typography>
        <IconButton edge="end" onClick={handleClose} aria-label="close" disabled={isLoading}>
          <CloseIcon />
        </IconButton>
      </Box>

      {!useProvidedVarieties && varietiesError && (
        <Box sx={{ p: 2, bgcolor: theme.palette.error.light, color: theme.palette.error.main }}>
          <Typography variant="body2">
            Error loading varieties:{' '}
            {varietiesError instanceof Error ? varietiesError.message : commonT('unknown-error')}
          </Typography>
        </Box>
      )}

      <DialogContent dividers sx={{ p: 0 }}>
        {isLoading && (
          <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '300px' }}>
            <CircularProgress />
          </Box>
        )}

        {!isLoading && (
          <Box component="form" sx={{ display: 'flex', height: '100%', px: 0 }}>
            <Box sx={{ width: '40%', borderRight: '0px solid #e0e0e0', p: 2 }}>
              <Typography variant="subtitle1" sx={{ mb: 1, fontSize: '16px', fontWeight: 500 }}>
                {receiveTranslation('varieties')}
              </Typography>
              <Typography variant="caption" color="text.secondary" sx={{ display: 'block', mb: 2 }}>
                {receiveTranslation('varieties-description')}
              </Typography>

              <List sx={{ pt: 0, mx: -2 }}>
                {varieties.map((variety) => (
                  <ListItem
                    key={variety.id}
                    disablePadding
                    sx={{
                      padding: '16px 24px',
                      mb: 0.5,
                      bgcolor: selectedVariety === variety.id ? theme.palette.customColors.lightGray : 'transparent',
                      color: renderVarietyColor(variety.id),
                      '&:hover': {
                        bgcolor: renderVarietyBgColor(variety.id),
                        cursor: isVarietyEditable(variety.id) ? 'pointer' : 'default',
                      },
                      display: isVarietyEditable(variety.id) ? 'flex' : 'none',
                      justifyContent: 'space-between',
                      alignItems: 'center',
                      alignSelf: 'stretch',
                      position: 'relative',
                      pl: 3,
                      transition: 'all 0.2s ease-in-out',
                      opacity: isVarietyEditable(variety.id) ? 1 : 0.6,
                      pointerEvents: isVarietyEditable(variety.id) ? 'auto' : 'none',
                    }}
                    onClick={() => {
                      if (allZeroOrNegativeWarning) return;
                      handleVarietySelect(variety.id);
                    }}
                  >
                    <ListItemText
                      primary={getVarietyLabel(variety.id)}
                      slotProps={{
                        primary: {
                          sx: {
                            color:
                              selectedVariety === variety.id ? theme.palette.customColors.blueHighlight : 'inherit',
                            fontWeight: selectedVariety === variety.id ? 500 : 400,
                          },
                        },
                      }}
                    />
                    {checkVarietyHasData(variety.id) && (
                      <CheckIcon
                        sx={{
                          color: theme.palette.customColors.blueHighlight,
                          fontSize: '1.2rem',
                        }}
                      />
                    )}
                  </ListItem>
                ))}
              </List>
            </Box>
            <Box sx={{ width: '60%', p: 2, bgcolor: theme.palette.customColors.lightGray }}>
              <Typography variant="subtitle1" sx={{ mb: 1 }}>
                {receiveTranslation('grade-weight')}
              </Typography>
              <Typography variant="caption" color="text.secondary" sx={{ display: 'block', mb: 2 }}>
                {receiveTranslation('grade-help-text')}
              </Typography>

              {allZeroOrNegativeWarning && (
                <Alert
                  severity="info"
                  sx={{
                    mb: 2,
                    bgcolor: theme.palette.customColors.lightRed,
                    color: theme.palette.customColors.secondary,
                    p: '4px 5px',
                    alignItems: 'center',
                  }}
                  icon={
                    <InfoOutlinedIcon
                      sx={{
                        color: theme.palette.customColors.secondary,
                        verticalAlign: 'middle',
                        fontSize: '1.5rem',
                        p: 0,
                      }}
                    />
                  }
                >
                  {allZeroOrNegativeWarningMessage}
                </Alert>
              )}

              <Box sx={{ mt: 2 }}>
                {selectedVariety && renderGradeInputsSide()}

                {!selectedVariety && varieties.length > 0 && (
                  <Typography color="text.secondary" sx={{ mt: 4, textAlign: 'center' }}>
                    {receiveTranslation('varieties-help-text')}
                  </Typography>
                )}

                {varieties.length === 0 && !isLoading && (
                  <Typography color="text.secondary" sx={{ mt: 4, textAlign: 'center' }}>
                    {receiveTranslation('no-varieties-available')}
                  </Typography>
                )}
              </Box>
            </Box>
          </Box>
        )}
      </DialogContent>

      {submitError && (
        <Box sx={{ p: 2, bgcolor: theme.palette.error.light, color: theme.palette.error.main }}>
          <Typography variant="body2">{submitError}</Typography>
        </Box>
      )}

      <DialogActions
        sx={{
          p: 3,
          display: 'flex',
          justifyContent: !isEditReceiving ? 'space-between' : 'flex-end',
          alignItems: 'center',
        }}
      >
        {/* Conditional Checkboxes Section - Only in Confirm Mode */}
        {!isEditReceiving && (
          <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center', justifyContent: 'center' }}>
            <Controller
              name="confirmReceiveHarvest"
              control={control}
              render={({ field }) => (
                <FormControlLabel
                  control={
                    <Checkbox
                      {...field}
                      checked={field.value}
                      onChange={(e) => field.onChange(e.target.checked)}
                      name="confirm-receive-harvest"
                      color="primary"
                      size="small"
                    />
                  }
                  label={receiveTranslation('confirm-receive-harvest')}
                  sx={{
                    alignItems: 'center',
                    '& .MuiFormControlLabel-label': {
                      fontSize: '14px',
                      lineHeight: 1.4,
                    },
                  }}
                />
              )}
            />

            <Controller
              name="awareUndoAction"
              control={control}
              render={({ field }) => (
                <FormControlLabel
                  control={
                    <Checkbox
                      {...field}
                      checked={field.value}
                      onChange={(e) => field.onChange(e.target.checked)}
                      name="aware-undo-action"
                      color="primary"
                      size="small"
                    />
                  }
                  label={receiveTranslation('aware-undo-action')}
                  sx={{
                    alignItems: 'center',
                    '& .MuiFormControlLabel-label': {
                      fontSize: '14px',
                      lineHeight: 1.4,
                    },
                  }}
                />
              )}
            />
          </Box>
        )}

        <Box sx={{ display: 'flex', gap: 1 }}>
          <Button
            onClick={handleClose}
            variant="outlined"
            disabled={isLoading}
            sx={{
              minWidth: '140px',
              px: 3,
              py: 1,
              fontSize: '0.875rem',
              fontWeight: 500,
            }}
          >
            {commonT('cancel-modal-btn')}
          </Button>

          <Button
            onClick={handleSubmit(onSubmit)}
            variant="contained"
            color="primary"
            disabled={!isButtonEnabled}
            startIcon={isSubmitting ? <CircularProgress size={20} color="inherit" /> : null}
            sx={{
              minWidth: '140px',
              px: 3,
              py: 1,
              fontSize: '0.875rem',
              fontWeight: 500,
            }}
            id={buttonConfig.id}
            className={buttonConfig.className}
          >
            {buttonConfig.text}
          </Button>
        </Box>
      </DialogActions>
    </Dialog>
  );
};
